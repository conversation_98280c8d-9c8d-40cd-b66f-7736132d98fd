import request from '@/utils/request';

//获取升降机
export const getLiftDataApi = (cabinetId) => {
  return request({
    url: `/factory/elevator/get/by/cabinetId/${cabinetId}`,
    method: 'get',
  });
}

//更新升降机
export const updateLiftDataApi = (data) => {
  return request({
    url: '/factory/elevator/update',
    method: 'put',
    data
  });
}

//获取提取器
export const getExtractorDataApi = (cabinetId) => {
  return request({
    url: `/factory/extractor/get/by/cabinetId/${cabinetId}`,
    method: 'get',
  });
}

//更新提取器
export const updateExtractorDataApi = (data) => {
  return request({
    url: '/factory/extractor/update',
    method: 'put',
    data
  });
}

//获取货柜
export const getHuoGuiDataApi = (cabinetId) => {
  return request({
    url: `/factory/cabinet/${cabinetId}`,
    method: 'get',
  });
}

//更新货柜
export const updateHuoGuiDataApi = (data) => {
  return request({
    url: '/factory/cabinet/update',
    method: 'put',
    data
  });
}



//出入口分页
export const getOutInPageApi = (params) => {
  return request({
    url: `/factory/cabinet/gate/table`,
    method: 'get',
    params
  });
}

//出入口新增
export const addOutInPageApi = (data) => {
  return request({
    url: '/factory/cabinet/gate/add',
    method: 'post',
    data
  });
}

//出入口编辑
export const editOutInPageApi = (data) => {
  return request({
    url: '/factory/cabinet/gate/update',
    method: 'put',
    data
  });
}

//出入口删除
export const deleteOutInPageApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/del/${id}`,
    method: 'delete',
  });
}

//出入口启停
export const adjustOutInStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/switch/${id}`,
    method: 'put',
  })
}

//安全门启停
export const adjustDoorStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/door/switch/${id}`,
    method: 'put',
  })
}

//激光指示器启停
export const adjustExitStatusApi = (id) => {
  return request({
    url: `/factory/cabinet/gate/pointer/switch/${id}`,
    method: 'put',
  })
}


//拣选指示器分页
export const getPointerPageApi = (params) => {
  return request({
    url: `/factory/pointer/get/cabinetGate/pointer`,
    method: 'get',
    params
  });
}

//拣选指示器更新
export const updatePointerPageApi = (data) => {
  return request({
    url: `/factory/pointer/update`,
    method: 'put',
    data
  });
}