<template>
  <div class="app-container">
    <el-tabs v-model="activeName" :stretch="true" type="card">
      <el-tab-pane label="托盘设置" :name="1"></el-tab-pane>
      <el-tab-pane label="托盘列表" :name="2"></el-tab-pane>
      <el-tab-pane label="托盘位" :name="3"></el-tab-pane>
    </el-tabs>

    <div style="display: flex; justify-content: center">
      <TraySet v-if="activeName == 1"></TraySet>
      <TrayList v-if="activeName == 2"></TrayList>
      <TrayStation v-if="activeName == 3"></TrayStation>
    </div>
  </div>
</template>

<script setup>
const TraySet = defineAsyncComponent(() => import("./TraySet.vue"));
const TrayList = defineAsyncComponent(() => import("./TrayList.vue"));
const TrayStation = defineAsyncComponent(() => import("./TrayStation.vue"));

let activeName = ref(1);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.el-tabs {
  width: 20%;
  margin: 0 auto;
}
</style>
