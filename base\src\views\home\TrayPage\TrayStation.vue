<template>
  <div class="oc" style="background-color: #fff">
    <div class="action">
      <div>
        <el-button type="primary" @click="handleAddEdit({})">添加</el-button>
        <el-button :disabled="multiple" @click="deleteBtn">删除</el-button>
      </div>
      <div class="selection">
        <SearchList
          v-model="searchForms.keywords"
          @update:modelValue="theResetPageFn"
        ></SearchList>
      </div>
    </div>

    <el-table :data="tableList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="60" align="center"></el-table-column>
      <el-table-column label="方向" prop="direction" align="center">
        <template #default="scope">
          <span>{{ directionObj[scope.row.direction] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="高度" prop="height" align="center"></el-table-column>
      <el-table-column
        label="层偏移量"
        prop="layerOffset"
        align="center"
      ></el-table-column>
      <el-table-column label="层号" prop="sort" align="center"></el-table-column>
      <el-table-column
        label="批量创建数量"
        prop="batchCount"
        align="center"
      ></el-table-column>
      <el-table-column label="托盘编号" prop="trayCode" align="center"></el-table-column>
      <el-table-column label="托盘位使用状态" prop="useStatus" align="center">
        <template #default="scope">
          <span>{{ scope.row.useStatus ? "已使用" : "未使用" }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" align="center">
        <template #default="scope">
          <div class="div-flex">
            <el-button class="table-btn" text @click="handleAddEdit(scope.row)">
              编辑
            </el-button>
            <el-button class="table-btn" text @click="deleteBtn(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row justify="end" style="margin-top: 10px">
      <el-pagination
        v-show="thePage.total > 0"
        background
        :total="thePage.total"
        v-model:current-page="thePage.current"
        v-model:page-size="thePage.size"
        @current-change="thePageFn"
        layout="total,prev, pager, next"
      ></el-pagination>
    </el-row>

    <StationModal ref="addEdieModalRef" @on-success="theResetPageFn"></StationModal>
  </div>
</template>

<script setup>
import { getTrayStationPageApi, deleteTrayStationPageApi } from "@/api/home/<USER>";
const StationModal = defineAsyncComponent(() => import("./modal/StationModal.vue"));
const { proxy } = getCurrentInstance();

let directionObj = ref({
  1: "前侧",
  2: "后侧",
});

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
const tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    cabinetId: localStorage.getItem("huogui.id"),
    pageNum: thePage.value.current,
    pageSize: thePage.value.size,
    keywords: searchForms.value.keywords,
  };
  let res = await getTrayStationPageApi(obj);
  tableList.value = res.rows;
  thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row);
};

//删除
const multiple = ref(true);
let deleteIds = ref([]);
const handleSelectionChange = (val) => {
  deleteIds.value = val.map((item) => item.trayMapId);
  multiple.value = val.length ? false : true;
};
const deleteBtn = (row) => {
  proxy.$modal
    .confirm(`是否确认删除此数据项？`)
    .then(() =>
      deleteTrayStationPageApi({
        ids: row.trayMapId ? [row.trayMapId] : deleteIds.value,
      })
    )
    .then(() => {
      proxy.$modal.msgSuccess(`删除成功`);
      thePageFn();
    })
    .catch(() => {});
};

onMounted(() => {
  thePageFn();
});
</script>

<style lang="scss" scoped></style>
