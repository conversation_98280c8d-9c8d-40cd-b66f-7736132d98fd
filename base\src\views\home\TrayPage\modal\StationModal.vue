<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 13:32:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:22:41
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\TrayPage\modal\ListMOdal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.trayMapId !== undefined ? '编辑托盘位' : '新增托盘位'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="80px">
          <el-form-item label="方向:" v-if="!addForm.trayMapId">
            <el-radio-group v-model="addForm.direction">
              <el-radio :label="1">前侧</el-radio>
              <el-radio :label="2">后侧</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="高度" v-if="!addForm.trayMapId">
            <el-input-number
              v-model="addForm.height"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="层偏移量">
            <el-input-number
              v-model="addForm.layerOffset"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="层号" v-if="!addForm.trayMapId">
            <el-input-number
              v-model="addForm.sort"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="批量创建数量" v-if="!addForm.trayMapId">
            <el-input-number
              v-model="addForm.batchCount"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="托盘号" v-if="addForm.trayMapId">
            <el-input-number
              v-model="addForm.trayCode"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="使用状态:" v-if="addForm.trayMapId">
            <el-radio-group v-model="addForm.useStatus">
              <el-radio :label="false">未使用</el-radio>
              <el-radio :label="true">已使用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getOutInApi } from "@/api/loginApi";
import { addTrayStationPageApi, editTrayStationPageApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
const store = useStore();

const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  direction: [{ required: true, message: "请选择", trigger: "blur" }],
});

//出入口
let OutInList = ref([]);
const getOutInApiFn = async (id) => {
  OutInList.value = await getOutInApi({
    cabinetId: localStorage.getItem("huogui.id"),
  });
};

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  getOutInApiFn();
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;
      if (addForm.value.trayMapId) {
        requestObj = editTrayStationPageApi({
          ...addForm.value,
          cabinetId: +localStorage.getItem("huogui.id"),
        });
      } else {
        requestObj = addTrayStationPageApi({
          ...addForm.value,
          cabinetId: +localStorage.getItem("huogui.id"),
        });
      }
      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
