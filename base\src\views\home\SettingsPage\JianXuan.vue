<template>
  <div>
    <el-dialog title="拣选指示器" v-model="dialogueFlag" width="1400px" append-to-body>
      <div class="oc" style="background-color: #fff; padding: 30px">
        <div class="action">
          <div>
            <el-button type="primary" @click="handleAddEdit({})">添加</el-button>
          </div>
          <div class="selection">
            <SearchList
              v-model="searchForms.keywords"
              @update:modelValue="theResetPageFn"
            ></SearchList>
          </div>
        </div>

        <el-table :data="tableList">
          <el-table-column
            label="X轴行程"
            prop="xAxisTravel"
            align="center"
          ></el-table-column>

          <el-table-column
            label="X轴手动速度"
            prop="xManualSpeed"
            align="center"
          ></el-table-column>
          <el-table-column
            label="X轴自动速度"
            prop="xAutoSpeed"
            align="center"
          ></el-table-column>
          <el-table-column
            label="X轴加速度"
            prop="xAcceleration"
            align="center"
          ></el-table-column>
          <el-table-column
            label="X轴减速度"
            prop="xDeceleration"
            align="center"
          ></el-table-column>
          <el-table-column
            label="Z轴行程"
            prop="zAxisTravel"
            align="center"
          ></el-table-column>
          <el-table-column
            label="z轴手动速度"
            prop="zManualSpeed"
            align="center"
          ></el-table-column>
          <el-table-column
            label="z轴自动速度"
            prop="zAutoSpeed"
            align="center"
          ></el-table-column>
          <el-table-column
            label="z轴加速度"
            prop="zAcceleration"
            align="center"
          ></el-table-column>
          <el-table-column
            label="z轴减速度"
            prop="zDeceleration"
            align="center"
          ></el-table-column>

          <el-table-column label="操作" width="200" align="center">
            <template #default="scope">
              <div class="div-flex">
                <el-button class="table-btn" text @click="handleAddEdit(scope.row)">
                  编辑
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-row justify="end" style="margin-top: 10px">
          <el-pagination
            v-show="thePage.total > 0"
            background
            :total="thePage.total"
            v-model:current-page="thePage.current"
            v-model:page-size="thePage.size"
            @current-change="thePageFn"
            layout="total,prev, pager, next"
          ></el-pagination>
        </el-row> -->
      </div>
    </el-dialog>
    <JianxuanModal ref="addEdieModalRef" @on-success="theResetPageFn"></JianxuanModal>
  </div>
</template>

<script setup>
import { getPointerPageApi } from "@/api/home/<USER>";
const JianxuanModal = defineAsyncComponent(() => import("./Modal/JianxuanModal.vue"));
const { proxy } = getCurrentInstance();

let dialogueFlag = ref(false);

//分页
const thePage = ref({
  current: 1,
  size: proxy.configs.pageSize,
  total: 0,
});
let  tableList = ref([]);
const thePageFn = async () => {
  let obj = {
    cabinetId: rowObj.value.cabinetId,
    cabinetGateId: rowObj.value.cabinetGateId,
  };
  let res = await getPointerPageApi(obj);
  console.log(res, "数据");
  tableList.value = [res];
  console.log(tableList.value, " tableList.value");
  // thePage.value.total = res.total;
};

//搜索
const searchForms = ref({
  keywords: "",
});
const theResetPageFn = () => {
  thePage.value.current = 1;
  thePageFn();
};

//编辑
const addEdieModalRef = ref(null);
const handleAddEdit = (row) => {
  addEdieModalRef.value.openModal(row, rowObj.value);
};

// 初始化弹窗
let rowObj = ref({});
function openModal(val) {
  rowObj.value = val;
  dialogueFlag.value = true;
  thePageFn();
}

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
