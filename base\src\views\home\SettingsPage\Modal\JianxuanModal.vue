<!--
 * @Author: 方志良 
 * @Date: 2025-07-01 13:32:52
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-02 16:22:41
 * @FilePath: \aosikai-cabinet-wms-web\base\src\views\home\TrayPage\modal\ListMOdal.vue
 * 
-->
<template>
  <div>
    <el-dialog
      :title="addForm.pointerId !== undefined ? '编辑拣选指示器' : '新增拣选指示器'"
      v-model="dialogueFlag"
      width="520px"
      append-to-body
    >
      <div style="padding: 30px">
        <el-form ref="ruleFormRef" :model="addForm" :rules="rules" label-width="120px">
          <el-form-item label="X轴行程" prop="xAxisTravel">
            <el-input-number
              v-model="addForm.xAxisTravel"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="X轴手动速度" prop="xManualSpeed">
            <el-input-number
              v-model="addForm.xManualSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="X轴自动速度" prop="xAutoSpeed">
            <el-input-number
              v-model="addForm.xAutoSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="X轴加速度" prop="xAcceleration">
            <el-input-number
              v-model="addForm.xAcceleration"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="X轴减速度" prop="xDeceleration">
            <el-input-number
              v-model="addForm.xDeceleration"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="Z轴行程" prop="zAxisTravel">
            <el-input-number
              v-model="addForm.zAxisTravel"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="z轴手动速度" prop="zManualSpeed">
            <el-input-number
              v-model="addForm.zManualSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="z轴自动速度" prop="zAutoSpeed">
            <el-input-number
              v-model="addForm.zAutoSpeed"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="z轴加速度" prop="zAcceleration">
            <el-input-number
              v-model="addForm.zAcceleration"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="z轴减速度" prop="zDeceleration">
            <el-input-number
              v-model="addForm.zDeceleration"
              controls-position="right"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="confirmLoading">
            确 定
          </el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updatePointerPageApi } from "@/api/home/<USER>";

import { useStore } from "vuex";
const store = useStore();

const { proxy } = getCurrentInstance();
let dialogueFlag = ref(false);
const addForm = ref({});
const rules = reactive({
  xAxisTravel: [{ required: true, message: "请输入", trigger: "blur" }],
  xManualSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  xAutoSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  xAcceleration: [{ required: true, message: "请输入", trigger: "blur" }],
  xDeceleration: [{ required: true, message: "请输入", trigger: "blur" }],
  zAxisTravel: [{ required: true, message: "请输入", trigger: "blur" }],
  zManualSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  zAutoSpeed: [{ required: true, message: "请输入", trigger: "blur" }],
  zAcceleration: [{ required: true, message: "请输入", trigger: "blur" }],
  zDeceleration: [{ required: true, message: "请输入", trigger: "blur" }],
});


// 初始化弹窗
let rowObj = ref({});
function openModal(val, row) {
  rowObj.value = row;
  const obj = Object.assign({}, val);
  addForm.value = obj;
  dialogueFlag.value = true;
  proxy.resetForm("ruleFormRef");
}

//确定
const emit = defineEmits(["on-success"]);
const ruleFormRef = ref(null);
let confirmLoading = ref(false);
const submitForm = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      confirmLoading.value = true;
      let requestObj = null;

      requestObj = updatePointerPageApi({
        ...addForm.value,
        cabinetId: rowObj.value.cabinetId,
        cabinetGateId: rowObj.value.cabinetGateId,
      });

      requestObj
        .then(() => {
          proxy.$modal.msgSuccess("操作成功");
          dialogueFlag.value = false;
          emit("on-success");
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
};

//取消
const cancel = () => {
  addForm.value = {};
  proxy.resetForm("ruleFormRef");
  dialogueFlag.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped></style>
