{"name": "<PERSON><PERSON><PERSON>", "version": "1.7.0", "description": "奥斯凯工业系统", "author": "valarchie", "license": "MIT", "scripts": {"dev": "vite --port 6767 --host 0.0.0.0", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "fix": "eslint src/**/*.* --fix"}, "dependencies": {"@element-plus/icons-vue": "1.1.4", "@vue/eslint-config-standard": "8.0.1", "@vueuse/core": "^9.3.0", "axios": "0.26.1", "echarts": "5.3.2", "element-plus": "2.2.20", "eslint-plugin-prettier": "4.2.1", "file-saver": "2.0.5", "fuse.js": "6.5.3", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "nprogress": "0.2.0", "qrcode.vue": "^3.4.1", "vue": "3.2.31", "vue-cropper": "1.0.3", "vue-router": "4.0.14", "vue3-print-nb": "^0.1.4", "vue3-seamless-scroll": "2.0.1", "vuex": "4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "2.3.1", "@vue/compiler-sfc": "3.2.31", "eslint": "^8.21.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "8.7.0", "eslint-config-standard": "^17.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.7.0", "prettier": "2.8.4", "sass": "1.50.0", "unplugin-auto-import": "0.6.9", "vite": "2.6.14", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.4.0"}}